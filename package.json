{"name": "home-view", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "serve": "vite", "build": "vite build", "preview": "vite preview", "storybook": "start-storybook -p 6006", "build-storybook": "build-storybook"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.2.0", "@fortawesome/free-brands-svg-icons": "^6.2.0", "@fortawesome/free-regular-svg-icons": "^6.2.0", "@fortawesome/free-solid-svg-icons": "^6.2.0", "@imengyu/vue3-context-menu": "^1.4.8", "@types/webpack-env": "^1.18.8", "core-js": "^3.8.3", "eventbusjs": "^0.2.0", "v-tooltip": "^2.1.3", "vue": "^3.2.13", "vue-router": "^4.0.3", "vuex": "^4.0.0", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.20.12", "@storybook/addon-actions": "^6.5.15", "@storybook/addon-essentials": "^6.5.15", "@storybook/addon-interactions": "^6.5.15", "@storybook/addon-links": "^6.5.15", "@storybook/builder-webpack5": "^6.5.15", "@storybook/manager-webpack5": "^6.5.15", "@storybook/testing-library": "^0.0.13", "@storybook/vue3": "^6.5.15", "@types/eventbusjs": "^0.2.0", "@types/node": "^22.15.30", "@types/vue": "^2.0.0", "@vitejs/plugin-vue": "^4.6.2", "dayjs": "^1.11.13", "emoji-mart-vue-fast": "^15.0.4", "emoji-regex": "^10.4.0", "file-type": "^20.4.1", "recorder-core": "^1.3.25011100", "sass": "^1.32.7", "typescript": "~5.0.4", "vite": "^5.4.19", "vite-plugin-svg-icons": "^2.0.1", "vue-datepicker-next": "^1.0.3", "wavesurfer.js": "^7.9.4"}}