import { Provide, <PERSON>ope, <PERSON><PERSON><PERSON><PERSON>, Inject } from "@midwayjs/core";
import { OSSService } from "@midwayjs/oss";
import { Readable } from "stream";
import { RedisService } from "@midwayjs/redis";
import { ILogger } from "@midwayjs/logger";
import { WebSocketService } from "../socket/websocket.service";
import { ACTION, MESSAGE_TYPE } from "../socket/constants";
import { ClientManagerService } from "./client-manager.service";
import { MessageMedia } from "./whatsapp-web";
import * as fs from "fs";

@Provide()
@Scope(ScopeEnum.Singleton)
export class MediaService {
  @Inject()
  ossService: OSSService;

  @Inject()
  private redisService: RedisService;

  @Inject()
  logger: ILogger;

  @Inject()
  private wsService: WebSocketService;

  @Inject()
  clientManager: ClientManagerService;

  // 上传媒体到 OSS（原 getMediaUrl 中的逻辑）
  async uploadMedia(buffer: Buffer, mimeType: string) {
    const stream = Readable.from(buffer);
    const result = await this.ossService.putStream(
      `${mimeType}/${Date.now()}_${Math.random().toString(36).slice(2)}`,
      stream
    );
    return `https://asskype.oss-ap-southeast-1.aliyuncs.com/${result.name}`;
  }

  public async getMediaUrl(wsMsgId: string, uuid: string, messageId: string):Promise<string> {
    try {
      const mediaRedisKey = `medias:${uuid}:${messageId}`;
      const cacheMedia = await this.redisService.get(mediaRedisKey);
      if (cacheMedia) {
        this.logger.info(`从缓存中获取到媒体文件`);
        if (wsMsgId) {
          this.wsService.sendMessageToUser(uuid, {
            id: wsMsgId,
            action: ACTION.MEDIALOADING,
            data: cacheMedia,
            messageType: MESSAGE_TYPE.RPC,
          });
        }

        return cacheMedia;
      }
      if (!this.clientManager.clientQueue.has(uuid)) {
        return null;
      }
      const { client } = this.clientManager.clientQueue.get(uuid);
      return new Promise((resolve) => {
        client.getMessageById(messageId).then((message) => {
          if (!message) {
            this.logger.error(`获取消息失败`);
            return;
          }
          message.downloadMedia().then((media) => {
            if (!media) {
              this.logger.error(`获取媒体文件失败`);
              return;
            }
            const buffer = Buffer.from(media.data, "base64");
            const stream = Readable.from(buffer);
            this.ossService
              .putStream(
                `${media.mimetype}/${
                  Date.now().toString() + Math.random().toString().substr(2, 5)
                }`,
                stream
              )
              .then((res) => {
                this.logger.info(`上传成功: ${res}`);
                const fileUrl = `https://asskype.oss-ap-southeast-1.aliyuncs.com/${res.name}`;
                this.redisService.set(mediaRedisKey, fileUrl);
                if (wsMsgId) {
                  this.wsService.sendMessageToUser(uuid, {
                    id: wsMsgId,
                    action: ACTION.MEDIALOADING,
                    data: fileUrl,
                    messageType: MESSAGE_TYPE.RPC,
                  });
                }
                resolve(fileUrl)
              });
          });
        });
      });
    } catch (e) {
      this.logger.error(`获取媒体文件失败：${e.message}`);
    }
    return ""
  }

  public async getProfilePicUrl(
    uuid: string,
    contactId: string,
    reload: boolean = false
  ) {
    try {
      this.logger.info("开始获取头像::::::", contactId);
      const redisKey = `profile_pic:${uuid}:${contactId}`;
      const cachePic = await this.redisService.get(redisKey);
      if (cachePic && !reload) {
        this.logger.info(`从缓存中获取到头像`);
        return cachePic;
      }
      if (!this.clientManager.clientQueue.has(uuid)) {
        return null;
      }

      const { client } = this.clientManager.clientQueue.get(uuid);
      const timeoutPromise = new Promise<string>((resolve, reject) => {
        client
          .getProfilePicUrl(contactId)
          .then((picurl) => {
            resolve(picurl);
          })
          .catch((e) => {
            this.logger.error(`获取头像失败：${e.message}`);
          });
        setTimeout(() => {
          this.logger.info("获取头像超时::::::", contactId);
          resolve("");
        }, 1500);
      });
      const picture = await timeoutPromise;
      if (picture == "") {
        this.logger.error(`获取头像失败`);
      }
      this.redisService.set(redisKey, picture);
      return picture;
    } catch (e) {
      this.logger.error(`获取头像失败：${e.message}`);
    }
  }

  public async getGroupUserPicUrl(wsMsgId: string, uuid: string, cid: string) {
    const redisKey = `profile_pic:${uuid}:${cid}`;
    const cacheUserInfo = await this.redisService.get(redisKey);
    if (cacheUserInfo) {
      this.wsService.sendMessageToUser(uuid, {
        id: wsMsgId,
        action: ACTION.GROUPPROFILEPICURL,
        data: cacheUserInfo,
        messageType: MESSAGE_TYPE.RPC,
      });
      return;
    }
    this.wsService.sendMessageToUser(uuid, {
      id: wsMsgId,
      action: ACTION.GROUPPROFILEPICURL,
      data: "",
      messageType: MESSAGE_TYPE.RPC,
    });
  }

  // 获取 messageMediaFile
  getMediaFile(uploadedFile: any) {
    const fileData = fs.readFileSync(uploadedFile.data).toString("base64");
    const messageMediaFile = new MessageMedia(
      uploadedFile.mimeType,
      fileData,
      uploadedFile.filename
    );
    return messageMediaFile;
  }

}
