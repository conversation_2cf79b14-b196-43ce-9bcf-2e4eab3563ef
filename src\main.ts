import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
// import registerIcons from '@/assets/icons'
// import vTooltip from 'floating-vue'
import ContextMenu from '@imengyu/vue3-context-menu'
import DialogPlugin from '@/utils/dialog-service';
// import "emoji-mart-vue-fast/css/emoji-mart.css";
// import '@imengyu/vue3-context-menu/lib/vue3-context-menu.css'

// import { library } from "@fortawesome/fontawesome-svg-core";
// import { fas } from '@fortawesome/free-solid-svg-icons'
// library.add(fas);
// import { fab } from '@fortawesome/free-brands-svg-icons';
// library.add(fab);
// import { far } from '@fortawesome/free-regular-svg-icons';
// library.add(far);
// import { dom } from "@fortawesome/fontawesome-svg-core";
// dom.watch();

const app = createApp(App)

// registerIcons(app)
console.log("==开始==")
app.use(store).use(router).use(ContextMenu).use(DialogPlugin).mount('#app')
