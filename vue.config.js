const { defineConfig } = require("@vue/cli-service");
const path = require("path");

module.exports = defineConfig({
  transpileDependencies: true,
  // devServer: {
  //   port: 8080,
  //   hot: true, // 确保启用热更新
  //   watchFiles: {
  //     paths: ['src/**/*', 'public/**/*'],
  //     options: {
  //       usePolling: true, // 在某些系统上更可靠
  //     }
  //   },
  // },
  /*devServer: {
    port: 8080, // 设置开发服务器端口
    proxy: {
      '/api': {
        target: 'http://localhost:7001', // 代理目标地址
        changeOrigin: true,
        pathRewrite: { '^/api': '' },
      },
    },
  },
  configureWebpack: {
    devtool: 'source-map', // 开发环境启用 Source Map
  },*/
  /*chainWebpack: (config) => {
    // 添加 ESLint 插件配置
    config.plugin('eslint').tap((args) => {
      args[0].fix = true; // 自动修复 ESLint 错误
      return args;
    });
  },*/
  chainWebpack: (config) => {
    // SVG 处理
    config.module
      .rule("svg")
      .exclude.add(path.resolve("src/assets/icons/svgs"))
      .end();

    config.module
      .rule("icons")
      .test(/\.svg$/)
      .include.add(path.resolve("src/assets/icons/svgs"))
      .end()
      .use("svg-sprite-loader")
      .loader("svg-sprite-loader")
      .options({
        symbolId: "icon-[name]",
      })
      .end();
  },
});
