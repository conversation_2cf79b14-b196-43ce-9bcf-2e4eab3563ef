{"compileOnSave": true, "compilerOptions": {"target": "es2018", "module": "commonjs", "moduleResolution": "node", "experimentalDecorators": true, "emitDecoratorMetadata": true, "inlineSourceMap": true, "noImplicitThis": true, "noUnusedLocals": true, "stripInternal": true, "skipLibCheck": true, "pretty": true, "declaration": true, "forceConsistentCasingInFileNames": true, "typeRoots": ["./typings", "./node_modules/@types"], "outDir": "dist", "rootDir": "src"}, "exclude": ["*.js", "*.ts", "dist", "node_modules", "test"]}