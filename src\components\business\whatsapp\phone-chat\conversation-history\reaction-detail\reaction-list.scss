.reaction-list {
  padding: $pix-8;
}

.reaction-item {
  display: flex;
  align-items: center;
  padding: $pix-8 $pix-12;
  border-radius: $pix-8;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: var(--background-default-hover);
  }
}

.reaction-item-avatar {
  margin-right: $pix-12;
}

.reaction-item-info {
  flex: 1;
  min-width: 0;
}

.reaction-item-name {
  font-size: $pix-14;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: $pix-2;
}

.reaction-item-time {
  font-size: $pix-12;
  color: var(--text-secondary);
}

.reaction-item-emoji {
  margin-left: $pix-8;
}
