<template>
  <div class="reaction-list">
    <div 
      v-for="sender in senders" 
      :key="sender.senderId"
      class="reaction-item"
      @click="onItemClick(sender)"
    >
      <div class="reaction-item-avatar">
        <user-avatar 
          :src="sender.profilePicUrl" 
          :name="sender.name"
          :size="40"
        />
      </div>
      <div class="reaction-item-info">
        <div class="reaction-item-name">{{ sender.name }}</div>
        <div class="reaction-item-time">{{ formatTime(sender.timestamp) }}</div>
      </div>
      <div class="reaction-item-emoji" v-if="sender.reaction">
        <emoji :emoji="sender.reaction" :size="20" />
      </div>
    </div>
  </div>
</template>

<script src="./reaction-list.ts" lang="ts"/>
<style src="./reaction-list.scss" lang="scss" scoped/>
