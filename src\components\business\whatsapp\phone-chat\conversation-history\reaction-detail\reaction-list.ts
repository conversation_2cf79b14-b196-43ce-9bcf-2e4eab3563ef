import { defineComponent } from "vue";
import UserAvatar from "@/components/common/user-avatar/user-avatar.vue";
import emoji from "@/components/common/emoji/emoji.vue";

export default defineComponent({
  components: { 
    UserAvatar, 
    emoji 
  },
  props: {
    senders: {
      type: Array,
      default: () => [],
    },
  },
  emits: ['itemClick'],
  setup(props, { emit }) {
    const onItemClick = (sender: any) => {
      emit('itemClick', sender);
    };

    const formatTime = (timestamp: number) => {
      if (!timestamp) return '';
      const date = new Date(timestamp * 1000);
      return date.toLocaleTimeString();
    };

    return {
      onItemClick,
      formatTime,
    };
  },
});
