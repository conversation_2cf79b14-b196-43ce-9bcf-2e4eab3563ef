@use "./_variables" as *;
@use "./whatsapp/overlay-phone-data"as *;
@use "./whatsapp/footer-phone-chat"as *;
@use "./whatsapp/dropdown-options"as *;
@use "./whatsapp/_datepicker-next"as *;
@use "./whatsapp/_common"as *;
@import url("https://fonts.googleapis.com/css?family=Roboto:300");

html {
    // user-select:none;
    box-sizing     : border-box;
    scroll-behavior: smooth;

    --panel-phone-chat-background-color: #ffffff;
    --panel-phone-chat-border-color    : #d1d7db;

    --panel-overlay-background-color                : hsla(0, 0%, 100%, 0.85);
    --panel-overlay-phone-data-background-color     : #ffffff;
    --panel-overlay-contacts-header-background-color: #1daa61;
    --panel-overlay-contacts-header-icon-color      : #ffffff;

    --panel-overlay-config-header-background-color                     : #1daa61;
    --panel-overlay-config-header-icon-color                           : #ffffff;
    --panel-overlay-config-summary-profile-data-info-title-font-color  : #111B21;
    --panel-overlay-config-summary-profile-data-info-message-font-color: #666781;
    --panel-overlay-config-options-icon-color                          : #8696a0;
    --panel-overlay-config-options-option-border-color                 : #f5f6f6;
    --panel-overlay-config-options-option-font-color                   : #111b21;
    --panel-overlay-config-options-hover-option-background-color       : #f5f6f6;

    --panel-modal-background-color  : #ffffff;
    --panel-modal-box-shadow-color-1: rgba(11, 20, 26, .19);
    --panel-modal-box-shadow-color-2: rgba(11, 20, 26, .24);
    --panel-modal-header-font-color : #3b4a54;
    --panel-modal-header-icon-color : #1daa61;

    --panel-modal-send-contact-list-contact-background-color: #fff;
    --panel-modal-send-contact-list-contact-title-font-color: #1daa61;

    --panel-overspread-preview-summary-content-options-icon-color: #54656f;

    --panel-header-background-color            : #FFFFFF;
    --panel-header-icon-color                  : #54656f;
    --panel-header-icon-select-background-color: #d1d7db;

    --panel-header-info-contact-alias-font-color  : #111B21;
    --panel-header-info-contact-on-line-font-color: #667781;

    --panel-notification-update-background-color      : #e1fef2;
    --panel-notification-update-icon-background-color : #00a884;
    --panel-notification-desktop-background-color     : #53bdeb;
    --panel-notification-desktop-icon-background-color: #ffffff;
    --panel-notification-icon-color                   : #ffffff;

    --panel-dropdown-menu-background-color                              : #ffffff;
    --panel-dropdown-menu-item-font-color                               : #3b4a54;
    --panel-dropdown-menu-item-hover-background-color                   : #f0f2f5;
    --dropdown-background-hover                                         : rgba(194, 189, 184, 0.15);
    --panel-search-conversations-background-color                       : #ffffff;
    --panel-search-conversations-search-text-background-color           : #f0f2f5;
    --panel-search-conversations-search-text-icon-color                 : #00a884;
    --panel-search-conversations-search-text-font-color                 : #3b4a54;
    --panel-search-conversations-search-option-icon-color               : #8696a0;
    --panel-search-conversations-search-option-selected-background-color: #00A884;
    --panel-search-conversations-search-option-selected-icon-color      : #ffffff;

    --panel-conversations-background-color: #ffffff;

    --panel-conversations-unread-background-color              : #ffffff;
    --panel-conversations-unread-border-color                  : #e9edef;
    --panel-conversations-unread-title-text-font-color         : #1daa61;
    --panel-conversations-unread-bottom-message-text-font-color: #3b4a54;

    --panel-contact-hover-background-color: #f5f6f6;
    --panel-contact-avatar-border-color   : #e9edef;

    --panel-contact-info-border-color                            : #e9edef;
    --panel-contact-info-data-name-font-color                    : #111B21;
    --panel-contact-info-data-last-message-font-color            : #666781;
    --panel-contact-hover-info-data-name-font-color              : #111B21;
    --panel-contact-hover-info-data-last-message-font-color      : #666781;
    --panel-contact-hover-info-data-last-message-icon-color      : rgba(0, 0, 0, .4);
    --panel-contact-unread-info-data-date-last-message-font-color: #1fa855;
    --panel-contact-unread-info-data-count-background-color      : #25d366;

    --panel-overlay-phone-chat-background-color   : #f0f2f5;
    --panel-overlay-intro-title-message-font-color: #41525D;
    --panel-overlay-intro-message-font-color      : #667781;

    --panel-conversation-history-background-color: #efeae2;

    --panel-button-bottom-background-color: #ffffff;
    --panel-button-bottom-font-color      : #888D90;

    --panel-preview-media-background-color                           : #e9edef;
    --panel-preview-media-header-icon-color                          : #54656f;
    --panel-preview-media-header-font-color                          : #111b21;
    --panel-preview-media-image-container-message-background-color   : #ffffff;
    --panel-preview-media-image-container-message-text-font-color    : #3b4a54;
    --panel-preview-media-document-container-background-color        : #f0f2f5;
    --panel-preview-media-document-container-preview-font-color      : #aebac1;
    --panel-preview-media-document-container-message-background-color: #ffffff;
    --panel-preview-media-document-container-message-text-font-color : #3b4a54;

    --panel-message-history-background-color                 : #dcf8c6;
    --panel-message-history-no-from-me-background-color      : #ffffff;
    --panel-message-history-container-message-date-font-color: #667781;
    --panel-message-history-text-font-color                  : #111b21;

    --panel-quoted-message-from-me-background-color   : #d1f4cc;
    --panel-quoted-message-no-from-me-background-color: #f5f6f6;

    --panel-quoted-conversation-font-color: #667781;

    --panel-audio-message-phones-background-color              : orange;
    --panel-audio-message-phones-profile-icon-font-color       : #6F8171;
    --panel-audio-message-audio-player-option-icon-color       : #6f8171;
    --panel-audio-message-audio-player-seek-duration-font-color: #8696a0;

    --panel-document-message-from-me-background-color       : #d1f4cc;
    --panel-document-message-no-from-me-background-color    : #f5f6f6;
    --panel-document-message-icon-color                     : 80, 101, 111;
    --panel-document-message-description-name-font-color    : #111B21;
    --panel-document-message-description-features-font-color: #8696A0;
    --panel-document-message-text-message-font-color        : #111B21;

    --panel-emojis-background-color: #f0f2f5;

    --panel-categories-emojis-icon-color                  : rgba(17, 37, 23, .22);
    --panel-categories-emojis-category-select-icon-color  : rgba(17, 37, 23, .42);
    --panel-categories-emojis-category-select-border-color: #1daa61;
    --panel-categories-emojis-title-category-font-color   : #8696A0;

    --panel-footer-background-color                   : #f0f2f5;
    --panel-footer-icon-color                         : #54656f;
    --panel-footer-icon-select-background-color       : #d1d7db;
    --panel-footer-message-multimedia-background-color: #ffffff;
    --panel-footer-message-multimedia-font-color      : #3b4a54;

    --panel-multimedia-options-tooltip-background-color: #3b4a54;
    --panel-multimedia-options-tooltip-font-color      : #fff;

    --primary-muted        : #667781;
    --primary-muted-rgb    : 102, 119, 129;
    --primary              : #3b4a54;
    --primary-rgb          : 59, 74, 84;
    --primary-strong       : #111b21;
    --primary-strong-rgb   : 17, 27, 33;
    --primary-stronger     : #111b21;
    --primary-stronger-rgb : 17, 27, 33;
    --primary-strongest    : #111b21;
    --primary-strongest-rgb: 17, 27, 33;
    --primary-title        : #41525d;
    --primary-title-rgb    : 65, 82, 93;

    --scroll-background-color: rgba(0, 0, 0, .2);

    --v-button-primary-background-color    : #1daa61;
    --v-button-primary-border-color        : #1daa61;
    --v-button-primary-font-color          : #ffffff;
    --v-button-secondary-background-color  : transparent;
    --v-button-secondary-border-color      : #e9edef;
    --v-button-secondary-font-color        : #1daa61;
    --button-primary-destructive-background: #ea0038;
    --v-radio-default-font-color           : #3b4a54;

    --attachment-type-audio-color            : #fa6533;
    --attachment-type-audio-color-rgb        : 250, 101, 51;
    --attachment-type-photos-color           : #007bfc;
    --attachment-type-photos-color-rgb       : 0, 123, 252;
    --attachment-type-camera-color           : #ff2e74;
    --attachment-type-camera-color-rgb       : 255, 46, 116;
    --attachment-type-documents-color        : #7f66ff;
    --attachment-type-documents-color-rgb    : 127, 102, 255;
    --attachment-type-catalog-color          : #3a5564;
    --attachment-type-catalog-color-rgb      : 58, 85, 100;
    --attachment-type-quick-replies-color    : #ffbc38;
    --attachment-type-quick-replies-color-rgb: 255, 188, 56;
    --attachment-type-orders-color           : #009de2;
    --attachment-type-orders-color-rgb       : 0, 157, 226;
    --attachment-type-contacts-color         : #009de2;
    --attachment-type-contacts-color-rgb     : 0, 157, 226;
    --attachment-type-polls-color            : #ffbc38;
    --attachment-type-polls-color-rgb        : 255, 188, 56;
    --attachment-type-stickers-color         : #02a698;
    --attachment-type-stickers-color-rgb     : 2, 166, 152;
    --border-default                         : #0000001a;
    --border-panel                           : #0000001a;
    --background-default                     : #fff;
    --background-default-hover               : #f7f5f3;
    --background-default-active              : rgba(194, 189, 184, 0.15);
    --teal                                   : #1daa61;
    --WDS-accent                             : #1daa61;
    --WDS-surface-highlight                  : rgba(194, 189, 184, .15);
    --WDS-surface-emphasized                 : #f7f5f3;
    --WDS-warm-gray-75                       : #f7f5f3;
    --WDS-green-500                          : #1daa61;
    --WDS-content-default                    : #0a0a0a;
    --WDS-content-deemphasized               : rgba(0, 0, 0, .6);
    --WDS-content-action-emphasized          : #1b8755;
    --text-secondary                         : #00000099;
    --text-secondary-emphasized              : #00000099;
    --secondary-lighter                      : #00000099;
    --padding-drawer-bottom                  : 32px;
    --intro-background                       : #f7f5f3;
    --background-lightest-hover              : #f7f5f3;
    --icon                                   : #00000099;
    --wallpaper-background                   : #f5f1eb;
    --shadow-light                           : #0000001a;

    --padding-drawer-bottom    : 32px;
    --intro-background         : #f7f5f3;
    --background-lightest-hover: #f7f5f3;

    --icon-bright-highlight                  : #00a884;
    --icon-bright-highlight-rgb              : 0, 168, 132;
    --round-icon-background                  : #00a884;
    --round-icon-background-rgb              : 0, 168, 132;
    --icon-header-illustration-main          : #06cf9c;
    --icon-header-illustration-main-rgb      : 6, 207, 156;
    --icon-header-illustration-background    : rgba(6, 207, 156, .15);
    --icon-header-illustration-background-rgb: 6, 207, 156;
    --icon-primary                           : #00a884;
    --icon-primary-rgb                       : 0, 168, 132;

    --unread-background           : white;
    --unread-background-rgb       : 255, 255, 255;
    --unread-bar-background       : rgba(255, 255, 255, .25);
    --unread-bar-background-rgb   : 255, 255, 255;
    --unread-timestamp            : #1fa855;
    --unread-timestamp-rgb        : 31, 168, 85;
    --unread-marker-background    : #25d366;
    --unread-marker-background-rgb: 37, 211, 102;
    --unread-marker-text          : white;
    --unread-marker-text-rgb      : 255, 255, 255;

    --height-pane-footer    : 62px;
    --chatlist-avatar-w     : 49px;
    --chatlist-avatar-h     : 49px;
    --chat-spacing-adj      : 18px;
    --chat-spacing          : 15px;
    --width-location-bubble : 276px;
    --width-max-album-column: 695px;
    --width-product-thumb   : 80px;

    --text-primary                 : #111b21;
    --text-primary-rgb             : 17, 27, 33;
    --text-secondary-emphasized    : #54656f;
    --text-secondary-emphasized-rgb: 84, 101, 111;
    --text-secondary               : #667781;
    --text-secondary-rgb           : 102, 119, 129;
    --text-teal                    : #008069;
    --text-teal-rgb                : 0, 128, 105;
    --text-success                 : #1fa855;
    --text-success-rgb             : 31, 168, 85;
    --text-critical                : #ea0038;
    --text-critical-rgb            : 234, 0, 56;
    --secondary                    : #667781;
    --secondary-rgb                : 102, 119, 129;
    --secondary-light              : #d1d7db;
    --secondary-light-rgb          : 209, 215, 219;
    --secondary-lighter            : #8696a0;
    --secondary-lighter-rgb        : 134, 150, 160;
    --secondary-stronger           : #3b4a54;
    --secondary-stronger-rgb       : 59, 74, 84;
    --security-icon-background     : #d3ede6;
    --security-icon-background-rgb : 211, 237, 230;
    --security-icon-lock           : #1fc4b1;
    --security-icon-lock-rgb       : 31, 196, 177;
    --security-icon-shield         : #f0faf7;
    --security-icon-shield-rgb     : 240, 250, 247;
    --security-icon-lock           : #1fc4b1;
    --security-icon-lock-rgb       : 31, 196, 177;
    --security-icon-shield         : #f0faf7;
    --security-icon-shield-rgb     : 240, 250, 247;

    --shadow          : #0b141a;
    --shadow-rgb      : 11, 20, 26;
    --shadow-light    : rgba(11, 20, 26, .08);
    --shadow-light-rgb: 11, 20, 26;

    --radius-app : 3px;
    --radius-menu: 18px;
    --outgoing-background: #d9fdd3;
    --teal-light      : #7ae3c3;
    --teal-light-rgb  : 122, 227, 195;
    --teal-lighter    : #00a884;
    --teal-lighter-rgb: 0, 168, 132;
    --teal            : #008069;
    --teal-rgb        : 0, 128, 105;

    --drawer-background-deep: #f0f2f5;

    --input-border-active    : #00a884;
    --input-border-active-rgb: 0, 168, 132;
    --input-border           : #667781;
    --input-border-rgb       : 102, 119, 129;

    --WDS-cool-gray-400      : #8d9599;
    --button-round-background: #00a884;

    --icon-lighter           : #8696a0;
    --icon-lighter-rgb       : 134, 150, 160;
    --icon-strong            : #54656f;
    --icon-strong-rgb        : 84, 101, 111;
    --panel-background-deeper: #fff;
    --icon-fixed-rgb         : 134, 150, 160;

    --desktop-upsell-call-btn           : rgba(84, 101, 111, .5);
    --desktop-upsell-call-btn-rgb       : 84, 101, 111;
    --navbar-item-active-background     : rgba(0, 0, 0, .05);
    --incoming-background               : #ffffff;
    --reactions-details-background      : #ffffff;
    --reactions-details-background-hover: #f7f5f3;

}


*,
*::before,
*::after {
    box-sizing: inherit;
}

body {
    margin     : 0;
    font-family: Roboto, sans-serif;
}

::-webkit-scrollbar {
    height: $pix-5;
    width : $pix-5;
}

::-webkit-scrollbar-thumb {
    //background: #25d366;
    background: var(--scroll-background-color);
}

.flex-row {
    display       : flex;
    flex-direction: row;

    &.justify-content-space-between {
        justify-content: space-between;
    }
}

.flex-column {
    display       : flex;
    flex-direction: column;
}

.flex-center {
    display        : flex;
    align-items    : center;
    justify-content: center;
}

.message-history-text {
    color        : var(--panel-message-history-text-font-color);
    overflow-wrap: break-word;
    white-space  : pre-wrap;
}

.space-message-history {
    display : inline-block;
    // width: $pix-60; 
}

.#{$app-prefix}-image {
    height: $per-100;
    width : $per-100;
}

ul {
    list-style: none;
    margin    : 0;
    padding   : 0;
}

//右键菜单
.mx-context-menu-item-wrapper {
    cursor: pointer;
}

.red-menu-item {
    .mx-context-menu-item:hover {
        background-color: rgba(184, 5, 49, 0.08);

        .mx-item-row {
            color: rgb(184, 5, 49);
        }
    }
}


.mx-context-menu-item {
    border-radius: 8px;

    .mx-item-row {
        color: var(--WDS-content-deemphasized);
    }

    .mx-icon-placeholder {
        margin-right: 10px;
    }
}

.mx-context-menu-item .label {
    font-size: 14.5px !important;
}

.context-menu-item-icon {
    font-size: 24px;
}

.mx-context-menu {

    min-width    : 169px !important;
    border-radius: var(--radius-menu) !important;
    box-shadow   : 0 2px 5px 0 rgba(var(--shadow-rgb), .26), 0 2px 10px 0 rgba(var(--shadow-rgb), .16) !important;
    padding      : 10px !important;
}